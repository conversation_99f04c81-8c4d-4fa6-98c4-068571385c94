# Copyright 2020 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
"""String constants describing contents of an event accumulator."""

# Arbitrary strings chosen to pass the type information of the tag from
# the backend to the frontend.
TENSORS = "tensors"
GRAPH = "graph"
META_GRAPH = "meta_graph"
RUN_METADATA = "run_metadata"

# Legacy (pre-tensor) tag types.
COMPRESSED_HISTOGRAMS = "distributions"
HISTOGRAMS = "histograms"
IMAGES = "images"
AUDIO = "audio"
SCALARS = "scalars"
