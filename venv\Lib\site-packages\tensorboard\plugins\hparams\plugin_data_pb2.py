# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/plugins/hparams/plugin_data.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorboard.plugins.hparams import api_pb2 as tensorboard_dot_plugins_dot_hparams_dot_api__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorboard/plugins/hparams/plugin_data.proto',
  package='tensorboard.hparams',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n-tensorboard/plugins/hparams/plugin_data.proto\x12\x13tensorboard.hparams\x1a%tensorboard/plugins/hparams/api.proto\x1a\x1cgoogle/protobuf/struct.proto\"\xe9\x01\n\x11HParamsPluginData\x12\x0f\n\x07version\x18\x01 \x01(\x05\x12\x35\n\nexperiment\x18\x02 \x01(\x0b\x32\x1f.tensorboard.hparams.ExperimentH\x00\x12\x43\n\x12session_start_info\x18\x03 \x01(\x0b\x32%.tensorboard.hparams.SessionStartInfoH\x00\x12?\n\x10session_end_info\x18\x04 \x01(\x0b\x32#.tensorboard.hparams.SessionEndInfoH\x00\x42\x06\n\x04\x64\x61ta\"\xf4\x01\n\x10SessionStartInfo\x12\x43\n\x07hparams\x18\x01 \x03(\x0b\x32\x32.tensorboard.hparams.SessionStartInfo.HparamsEntry\x12\x11\n\tmodel_uri\x18\x02 \x01(\t\x12\x13\n\x0bmonitor_url\x18\x03 \x01(\t\x12\x12\n\ngroup_name\x18\x04 \x01(\t\x12\x17\n\x0fstart_time_secs\x18\x05 \x01(\x01\x1a\x46\n\x0cHparamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01\"T\n\x0eSessionEndInfo\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.tensorboard.hparams.Status\x12\x15\n\rend_time_secs\x18\x02 \x01(\x01\x62\x06proto3')
  ,
  dependencies=[tensorboard_dot_plugins_dot_hparams_dot_api__pb2.DESCRIPTOR,google_dot_protobuf_dot_struct__pb2.DESCRIPTOR,])




_HPARAMSPLUGINDATA = _descriptor.Descriptor(
  name='HParamsPluginData',
  full_name='tensorboard.hparams.HParamsPluginData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='version', full_name='tensorboard.hparams.HParamsPluginData.version', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='experiment', full_name='tensorboard.hparams.HParamsPluginData.experiment', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='session_start_info', full_name='tensorboard.hparams.HParamsPluginData.session_start_info', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='session_end_info', full_name='tensorboard.hparams.HParamsPluginData.session_end_info', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='data', full_name='tensorboard.hparams.HParamsPluginData.data',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=140,
  serialized_end=373,
)


_SESSIONSTARTINFO_HPARAMSENTRY = _descriptor.Descriptor(
  name='HparamsEntry',
  full_name='tensorboard.hparams.SessionStartInfo.HparamsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorboard.hparams.SessionStartInfo.HparamsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorboard.hparams.SessionStartInfo.HparamsEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=550,
  serialized_end=620,
)

_SESSIONSTARTINFO = _descriptor.Descriptor(
  name='SessionStartInfo',
  full_name='tensorboard.hparams.SessionStartInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='hparams', full_name='tensorboard.hparams.SessionStartInfo.hparams', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_uri', full_name='tensorboard.hparams.SessionStartInfo.model_uri', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='monitor_url', full_name='tensorboard.hparams.SessionStartInfo.monitor_url', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='group_name', full_name='tensorboard.hparams.SessionStartInfo.group_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time_secs', full_name='tensorboard.hparams.SessionStartInfo.start_time_secs', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_SESSIONSTARTINFO_HPARAMSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=376,
  serialized_end=620,
)


_SESSIONENDINFO = _descriptor.Descriptor(
  name='SessionEndInfo',
  full_name='tensorboard.hparams.SessionEndInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='tensorboard.hparams.SessionEndInfo.status', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time_secs', full_name='tensorboard.hparams.SessionEndInfo.end_time_secs', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=622,
  serialized_end=706,
)

_HPARAMSPLUGINDATA.fields_by_name['experiment'].message_type = tensorboard_dot_plugins_dot_hparams_dot_api__pb2._EXPERIMENT
_HPARAMSPLUGINDATA.fields_by_name['session_start_info'].message_type = _SESSIONSTARTINFO
_HPARAMSPLUGINDATA.fields_by_name['session_end_info'].message_type = _SESSIONENDINFO
_HPARAMSPLUGINDATA.oneofs_by_name['data'].fields.append(
  _HPARAMSPLUGINDATA.fields_by_name['experiment'])
_HPARAMSPLUGINDATA.fields_by_name['experiment'].containing_oneof = _HPARAMSPLUGINDATA.oneofs_by_name['data']
_HPARAMSPLUGINDATA.oneofs_by_name['data'].fields.append(
  _HPARAMSPLUGINDATA.fields_by_name['session_start_info'])
_HPARAMSPLUGINDATA.fields_by_name['session_start_info'].containing_oneof = _HPARAMSPLUGINDATA.oneofs_by_name['data']
_HPARAMSPLUGINDATA.oneofs_by_name['data'].fields.append(
  _HPARAMSPLUGINDATA.fields_by_name['session_end_info'])
_HPARAMSPLUGINDATA.fields_by_name['session_end_info'].containing_oneof = _HPARAMSPLUGINDATA.oneofs_by_name['data']
_SESSIONSTARTINFO_HPARAMSENTRY.fields_by_name['value'].message_type = google_dot_protobuf_dot_struct__pb2._VALUE
_SESSIONSTARTINFO_HPARAMSENTRY.containing_type = _SESSIONSTARTINFO
_SESSIONSTARTINFO.fields_by_name['hparams'].message_type = _SESSIONSTARTINFO_HPARAMSENTRY
_SESSIONENDINFO.fields_by_name['status'].enum_type = tensorboard_dot_plugins_dot_hparams_dot_api__pb2._STATUS
DESCRIPTOR.message_types_by_name['HParamsPluginData'] = _HPARAMSPLUGINDATA
DESCRIPTOR.message_types_by_name['SessionStartInfo'] = _SESSIONSTARTINFO
DESCRIPTOR.message_types_by_name['SessionEndInfo'] = _SESSIONENDINFO
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

HParamsPluginData = _reflection.GeneratedProtocolMessageType('HParamsPluginData', (_message.Message,), {
  'DESCRIPTOR' : _HPARAMSPLUGINDATA,
  '__module__' : 'tensorboard.plugins.hparams.plugin_data_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.HParamsPluginData)
  })
_sym_db.RegisterMessage(HParamsPluginData)

SessionStartInfo = _reflection.GeneratedProtocolMessageType('SessionStartInfo', (_message.Message,), {

  'HparamsEntry' : _reflection.GeneratedProtocolMessageType('HparamsEntry', (_message.Message,), {
    'DESCRIPTOR' : _SESSIONSTARTINFO_HPARAMSENTRY,
    '__module__' : 'tensorboard.plugins.hparams.plugin_data_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.hparams.SessionStartInfo.HparamsEntry)
    })
  ,
  'DESCRIPTOR' : _SESSIONSTARTINFO,
  '__module__' : 'tensorboard.plugins.hparams.plugin_data_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.SessionStartInfo)
  })
_sym_db.RegisterMessage(SessionStartInfo)
_sym_db.RegisterMessage(SessionStartInfo.HparamsEntry)

SessionEndInfo = _reflection.GeneratedProtocolMessageType('SessionEndInfo', (_message.Message,), {
  'DESCRIPTOR' : _SESSIONENDINFO,
  '__module__' : 'tensorboard.plugins.hparams.plugin_data_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.SessionEndInfo)
  })
_sym_db.RegisterMessage(SessionEndInfo)


_SESSIONSTARTINFO_HPARAMSENTRY._options = None
# @@protoc_insertion_point(module_scope)
