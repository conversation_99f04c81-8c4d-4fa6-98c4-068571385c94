# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/data/proto/data_provider.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from tensorboard.compat.proto import summary_pb2 as tensorboard_dot_compat_dot_proto_dot_summary__pb2
try:
  tensorboard_dot_compat_dot_proto_dot_histogram__pb2 = tensorboard_dot_compat_dot_proto_dot_summary__pb2.tensorboard_dot_compat_dot_proto_dot_histogram__pb2
except AttributeError:
  tensorboard_dot_compat_dot_proto_dot_histogram__pb2 = tensorboard_dot_compat_dot_proto_dot_summary__pb2.tensorboard.compat.proto.histogram_pb2
from tensorboard.compat.proto import tensor_pb2 as tensorboard_dot_compat_dot_proto_dot_tensor__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorboard/data/proto/data_provider.proto',
  package='tensorboard.data',
  syntax='proto3',
  serialized_options=_b('ZLgithub.com/tensorflow/tensorboard/tensorboard/data/proto/data_provider_proto'),
  serialized_pb=_b('\n*tensorboard/data/proto/data_provider.proto\x12\x10tensorboard.data\x1a\x1fgoogle/protobuf/timestamp.proto\x1a&tensorboard/compat/proto/summary.proto\x1a%tensorboard/compat/proto/tensor.proto\"-\n\x14GetExperimentRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\"\x84\x01\n\x15GetExperimentResponse\x12\x15\n\rdata_location\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x31\n\rcreation_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"#\n\x0cPluginFilter\x12\x13\n\x0bplugin_name\x18\x01 \x01(\t\"d\n\x0cRunTagFilter\x12)\n\x04runs\x18\x01 \x01(\x0b\x32\x1b.tensorboard.data.RunFilter\x12)\n\x04tags\x18\x02 \x01(\x0b\x32\x1b.tensorboard.data.TagFilter\"\x1a\n\tRunFilter\x12\r\n\x05names\x18\x01 \x03(\t\"\x1a\n\tTagFilter\x12\r\n\x05names\x18\x01 \x03(\t\" \n\nDownsample\x12\x12\n\nnum_points\x18\x01 \x01(\x03\"+\n\x12ListPluginsRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\"@\n\x13ListPluginsResponse\x12)\n\x07plugins\x18\x01 \x03(\x0b\x32\x18.tensorboard.data.Plugin\"\x16\n\x06Plugin\x12\x0c\n\x04name\x18\x01 \x01(\t\"(\n\x0fListRunsRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\"7\n\x10ListRunsResponse\x12#\n\x04runs\x18\x01 \x03(\x0b\x32\x15.tensorboard.data.Run\"1\n\x03Run\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x12\n\nstart_time\x18\x03 \x01(\x01J\x04\x08\x01\x10\x02R\x02id\"\x9a\x01\n\x12ListScalarsRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x35\n\rplugin_filter\x18\x02 \x01(\x0b\x32\x1e.tensorboard.data.PluginFilter\x12\x36\n\x0erun_tag_filter\x18\x03 \x01(\x0b\x32\x1e.tensorboard.data.RunTagFilter\"\x81\x02\n\x13ListScalarsResponse\x12<\n\x04runs\x18\x01 \x03(\x0b\x32..tensorboard.data.ListScalarsResponse.RunEntry\x1aZ\n\x08RunEntry\x12\x10\n\x08run_name\x18\x01 \x01(\t\x12<\n\x04tags\x18\x02 \x03(\x0b\x32..tensorboard.data.ListScalarsResponse.TagEntry\x1aP\n\x08TagEntry\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12\x32\n\x08metadata\x18\x02 \x01(\x0b\x32 .tensorboard.data.ScalarMetadata\"q\n\x0eScalarMetadata\x12\x10\n\x08max_step\x18\x01 \x01(\x03\x12\x15\n\rmax_wall_time\x18\x02 \x01(\x01\x12\x36\n\x10summary_metadata\x18\x03 \x01(\x0b\x32\x1c.tensorboard.SummaryMetadata\"\xcc\x01\n\x12ReadScalarsRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x35\n\rplugin_filter\x18\x02 \x01(\x0b\x32\x1e.tensorboard.data.PluginFilter\x12\x36\n\x0erun_tag_filter\x18\x03 \x01(\x0b\x32\x1e.tensorboard.data.RunTagFilter\x12\x30\n\ndownsample\x18\x04 \x01(\x0b\x32\x1c.tensorboard.data.Downsample\"\xf9\x01\n\x13ReadScalarsResponse\x12<\n\x04runs\x18\x01 \x03(\x0b\x32..tensorboard.data.ReadScalarsResponse.RunEntry\x1aZ\n\x08RunEntry\x12\x10\n\x08run_name\x18\x01 \x01(\t\x12<\n\x04tags\x18\x02 \x03(\x0b\x32..tensorboard.data.ReadScalarsResponse.TagEntry\x1aH\n\x08TagEntry\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12*\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x1c.tensorboard.data.ScalarData\"<\n\nScalarData\x12\x0c\n\x04step\x18\x01 \x03(\x03\x12\x11\n\twall_time\x18\x02 \x03(\x01\x12\r\n\x05value\x18\x03 \x03(\x02\"\x9a\x01\n\x12ListTensorsRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x35\n\rplugin_filter\x18\x02 \x01(\x0b\x32\x1e.tensorboard.data.PluginFilter\x12\x36\n\x0erun_tag_filter\x18\x03 \x01(\x0b\x32\x1e.tensorboard.data.RunTagFilter\"\x81\x02\n\x13ListTensorsResponse\x12<\n\x04runs\x18\x01 \x03(\x0b\x32..tensorboard.data.ListTensorsResponse.RunEntry\x1aZ\n\x08RunEntry\x12\x10\n\x08run_name\x18\x01 \x01(\t\x12<\n\x04tags\x18\x02 \x03(\x0b\x32..tensorboard.data.ListTensorsResponse.TagEntry\x1aP\n\x08TagEntry\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12\x32\n\x08metadata\x18\x02 \x01(\x0b\x32 .tensorboard.data.TensorMetadata\"q\n\x0eTensorMetadata\x12\x10\n\x08max_step\x18\x01 \x01(\x03\x12\x15\n\rmax_wall_time\x18\x02 \x01(\x01\x12\x36\n\x10summary_metadata\x18\x03 \x01(\x0b\x32\x1c.tensorboard.SummaryMetadata\"\xcc\x01\n\x12ReadTensorsRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x35\n\rplugin_filter\x18\x02 \x01(\x0b\x32\x1e.tensorboard.data.PluginFilter\x12\x36\n\x0erun_tag_filter\x18\x03 \x01(\x0b\x32\x1e.tensorboard.data.RunTagFilter\x12\x30\n\ndownsample\x18\x04 \x01(\x0b\x32\x1c.tensorboard.data.Downsample\"\xf9\x01\n\x13ReadTensorsResponse\x12<\n\x04runs\x18\x01 \x03(\x0b\x32..tensorboard.data.ReadTensorsResponse.RunEntry\x1aZ\n\x08RunEntry\x12\x10\n\x08run_name\x18\x01 \x01(\t\x12<\n\x04tags\x18\x02 \x03(\x0b\x32..tensorboard.data.ReadTensorsResponse.TagEntry\x1aH\n\x08TagEntry\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12*\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x1c.tensorboard.data.TensorData\"V\n\nTensorData\x12\x0c\n\x04step\x18\x01 \x03(\x03\x12\x11\n\twall_time\x18\x02 \x03(\x01\x12\'\n\x05value\x18\x03 \x03(\x0b\x32\x18.tensorboard.TensorProto\"\xa0\x01\n\x18ListBlobSequencesRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x35\n\rplugin_filter\x18\x02 \x01(\x0b\x32\x1e.tensorboard.data.PluginFilter\x12\x36\n\x0erun_tag_filter\x18\x03 \x01(\x0b\x32\x1e.tensorboard.data.RunTagFilter\"\x99\x02\n\x19ListBlobSequencesResponse\x12\x42\n\x04runs\x18\x01 \x03(\x0b\x32\x34.tensorboard.data.ListBlobSequencesResponse.RunEntry\x1a`\n\x08RunEntry\x12\x10\n\x08run_name\x18\x01 \x01(\t\x12\x42\n\x04tags\x18\x02 \x03(\x0b\x32\x34.tensorboard.data.ListBlobSequencesResponse.TagEntry\x1aV\n\x08TagEntry\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12\x38\n\x08metadata\x18\x02 \x01(\x0b\x32&.tensorboard.data.BlobSequenceMetadata\"\x8b\x01\n\x14\x42lobSequenceMetadata\x12\x10\n\x08max_step\x18\x01 \x01(\x03\x12\x15\n\rmax_wall_time\x18\x02 \x01(\x01\x12\x12\n\nmax_length\x18\x03 \x01(\x03\x12\x36\n\x10summary_metadata\x18\x04 \x01(\x0b\x32\x1c.tensorboard.SummaryMetadata\"\xd2\x01\n\x18ReadBlobSequencesRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x35\n\rplugin_filter\x18\x02 \x01(\x0b\x32\x1e.tensorboard.data.PluginFilter\x12\x36\n\x0erun_tag_filter\x18\x03 \x01(\x0b\x32\x1e.tensorboard.data.RunTagFilter\x12\x30\n\ndownsample\x18\x04 \x01(\x0b\x32\x1c.tensorboard.data.Downsample\"\x91\x02\n\x19ReadBlobSequencesResponse\x12\x42\n\x04runs\x18\x01 \x03(\x0b\x32\x34.tensorboard.data.ReadBlobSequencesResponse.RunEntry\x1a`\n\x08RunEntry\x12\x10\n\x08run_name\x18\x01 \x01(\t\x12\x42\n\x04tags\x18\x02 \x03(\x0b\x32\x34.tensorboard.data.ReadBlobSequencesResponse.TagEntry\x1aN\n\x08TagEntry\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12\x30\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\".tensorboard.data.BlobSequenceData\"l\n\x10\x42lobSequenceData\x12\x0c\n\x04step\x18\x01 \x03(\x03\x12\x11\n\twall_time\x18\x02 \x03(\x01\x12\x37\n\x06values\x18\x03 \x03(\x0b\x32\'.tensorboard.data.BlobReferenceSequence\"K\n\x15\x42lobReferenceSequence\x12\x32\n\tblob_refs\x18\x01 \x03(\x0b\x32\x1f.tensorboard.data.BlobReference\".\n\rBlobReference\x12\x10\n\x08\x62lob_key\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\"#\n\x0fReadBlobRequest\x12\x10\n\x08\x62lob_key\x18\x01 \x01(\t\" \n\x10ReadBlobResponse\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\x0c\x32\xdd\x07\n\x17TensorBoardDataProvider\x12`\n\rGetExperiment\x12&.tensorboard.data.GetExperimentRequest\x1a\'.tensorboard.data.GetExperimentResponse\x12\\\n\x0bListPlugins\x12$.tensorboard.data.ListPluginsRequest\x1a%.tensorboard.data.ListPluginsResponse\"\x00\x12S\n\x08ListRuns\x12!.tensorboard.data.ListRunsRequest\x1a\".tensorboard.data.ListRunsResponse\"\x00\x12\\\n\x0bListScalars\x12$.tensorboard.data.ListScalarsRequest\x1a%.tensorboard.data.ListScalarsResponse\"\x00\x12\\\n\x0bReadScalars\x12$.tensorboard.data.ReadScalarsRequest\x1a%.tensorboard.data.ReadScalarsResponse\"\x00\x12\\\n\x0bListTensors\x12$.tensorboard.data.ListTensorsRequest\x1a%.tensorboard.data.ListTensorsResponse\"\x00\x12\\\n\x0bReadTensors\x12$.tensorboard.data.ReadTensorsRequest\x1a%.tensorboard.data.ReadTensorsResponse\"\x00\x12n\n\x11ListBlobSequences\x12*.tensorboard.data.ListBlobSequencesRequest\x1a+.tensorboard.data.ListBlobSequencesResponse\"\x00\x12n\n\x11ReadBlobSequences\x12*.tensorboard.data.ReadBlobSequencesRequest\x1a+.tensorboard.data.ReadBlobSequencesResponse\"\x00\x12U\n\x08ReadBlob\x12!.tensorboard.data.ReadBlobRequest\x1a\".tensorboard.data.ReadBlobResponse\"\x00\x30\x01\x42NZLgithub.com/tensorflow/tensorboard/tensorboard/data/proto/data_provider_protob\x06proto3')
  ,
  dependencies=[google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,tensorboard_dot_compat_dot_proto_dot_summary__pb2.DESCRIPTOR,tensorboard_dot_compat_dot_proto_dot_tensor__pb2.DESCRIPTOR,])




_GETEXPERIMENTREQUEST = _descriptor.Descriptor(
  name='GetExperimentRequest',
  full_name='tensorboard.data.GetExperimentRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='experiment_id', full_name='tensorboard.data.GetExperimentRequest.experiment_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=176,
  serialized_end=221,
)


_GETEXPERIMENTRESPONSE = _descriptor.Descriptor(
  name='GetExperimentResponse',
  full_name='tensorboard.data.GetExperimentResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='data_location', full_name='tensorboard.data.GetExperimentResponse.data_location', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='tensorboard.data.GetExperimentResponse.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='tensorboard.data.GetExperimentResponse.description', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='creation_time', full_name='tensorboard.data.GetExperimentResponse.creation_time', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=224,
  serialized_end=356,
)


_PLUGINFILTER = _descriptor.Descriptor(
  name='PluginFilter',
  full_name='tensorboard.data.PluginFilter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plugin_name', full_name='tensorboard.data.PluginFilter.plugin_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=358,
  serialized_end=393,
)


_RUNTAGFILTER = _descriptor.Descriptor(
  name='RunTagFilter',
  full_name='tensorboard.data.RunTagFilter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='runs', full_name='tensorboard.data.RunTagFilter.runs', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tags', full_name='tensorboard.data.RunTagFilter.tags', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=395,
  serialized_end=495,
)


_RUNFILTER = _descriptor.Descriptor(
  name='RunFilter',
  full_name='tensorboard.data.RunFilter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='names', full_name='tensorboard.data.RunFilter.names', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=497,
  serialized_end=523,
)


_TAGFILTER = _descriptor.Descriptor(
  name='TagFilter',
  full_name='tensorboard.data.TagFilter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='names', full_name='tensorboard.data.TagFilter.names', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=525,
  serialized_end=551,
)


_DOWNSAMPLE = _descriptor.Descriptor(
  name='Downsample',
  full_name='tensorboard.data.Downsample',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='num_points', full_name='tensorboard.data.Downsample.num_points', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=553,
  serialized_end=585,
)


_LISTPLUGINSREQUEST = _descriptor.Descriptor(
  name='ListPluginsRequest',
  full_name='tensorboard.data.ListPluginsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='experiment_id', full_name='tensorboard.data.ListPluginsRequest.experiment_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=587,
  serialized_end=630,
)


_LISTPLUGINSRESPONSE = _descriptor.Descriptor(
  name='ListPluginsResponse',
  full_name='tensorboard.data.ListPluginsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plugins', full_name='tensorboard.data.ListPluginsResponse.plugins', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=632,
  serialized_end=696,
)


_PLUGIN = _descriptor.Descriptor(
  name='Plugin',
  full_name='tensorboard.data.Plugin',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='tensorboard.data.Plugin.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=698,
  serialized_end=720,
)


_LISTRUNSREQUEST = _descriptor.Descriptor(
  name='ListRunsRequest',
  full_name='tensorboard.data.ListRunsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='experiment_id', full_name='tensorboard.data.ListRunsRequest.experiment_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=722,
  serialized_end=762,
)


_LISTRUNSRESPONSE = _descriptor.Descriptor(
  name='ListRunsResponse',
  full_name='tensorboard.data.ListRunsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='runs', full_name='tensorboard.data.ListRunsResponse.runs', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=764,
  serialized_end=819,
)


_RUN = _descriptor.Descriptor(
  name='Run',
  full_name='tensorboard.data.Run',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='tensorboard.data.Run.name', index=0,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time', full_name='tensorboard.data.Run.start_time', index=1,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=821,
  serialized_end=870,
)


_LISTSCALARSREQUEST = _descriptor.Descriptor(
  name='ListScalarsRequest',
  full_name='tensorboard.data.ListScalarsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='experiment_id', full_name='tensorboard.data.ListScalarsRequest.experiment_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plugin_filter', full_name='tensorboard.data.ListScalarsRequest.plugin_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='run_tag_filter', full_name='tensorboard.data.ListScalarsRequest.run_tag_filter', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=873,
  serialized_end=1027,
)


_LISTSCALARSRESPONSE_RUNENTRY = _descriptor.Descriptor(
  name='RunEntry',
  full_name='tensorboard.data.ListScalarsResponse.RunEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='run_name', full_name='tensorboard.data.ListScalarsResponse.RunEntry.run_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tags', full_name='tensorboard.data.ListScalarsResponse.RunEntry.tags', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1115,
  serialized_end=1205,
)

_LISTSCALARSRESPONSE_TAGENTRY = _descriptor.Descriptor(
  name='TagEntry',
  full_name='tensorboard.data.ListScalarsResponse.TagEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='tensorboard.data.ListScalarsResponse.TagEntry.tag_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='metadata', full_name='tensorboard.data.ListScalarsResponse.TagEntry.metadata', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1207,
  serialized_end=1287,
)

_LISTSCALARSRESPONSE = _descriptor.Descriptor(
  name='ListScalarsResponse',
  full_name='tensorboard.data.ListScalarsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='runs', full_name='tensorboard.data.ListScalarsResponse.runs', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_LISTSCALARSRESPONSE_RUNENTRY, _LISTSCALARSRESPONSE_TAGENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1030,
  serialized_end=1287,
)


_SCALARMETADATA = _descriptor.Descriptor(
  name='ScalarMetadata',
  full_name='tensorboard.data.ScalarMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='max_step', full_name='tensorboard.data.ScalarMetadata.max_step', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_wall_time', full_name='tensorboard.data.ScalarMetadata.max_wall_time', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='summary_metadata', full_name='tensorboard.data.ScalarMetadata.summary_metadata', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1289,
  serialized_end=1402,
)


_READSCALARSREQUEST = _descriptor.Descriptor(
  name='ReadScalarsRequest',
  full_name='tensorboard.data.ReadScalarsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='experiment_id', full_name='tensorboard.data.ReadScalarsRequest.experiment_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plugin_filter', full_name='tensorboard.data.ReadScalarsRequest.plugin_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='run_tag_filter', full_name='tensorboard.data.ReadScalarsRequest.run_tag_filter', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='downsample', full_name='tensorboard.data.ReadScalarsRequest.downsample', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1405,
  serialized_end=1609,
)


_READSCALARSRESPONSE_RUNENTRY = _descriptor.Descriptor(
  name='RunEntry',
  full_name='tensorboard.data.ReadScalarsResponse.RunEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='run_name', full_name='tensorboard.data.ReadScalarsResponse.RunEntry.run_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tags', full_name='tensorboard.data.ReadScalarsResponse.RunEntry.tags', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1697,
  serialized_end=1787,
)

_READSCALARSRESPONSE_TAGENTRY = _descriptor.Descriptor(
  name='TagEntry',
  full_name='tensorboard.data.ReadScalarsResponse.TagEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='tensorboard.data.ReadScalarsResponse.TagEntry.tag_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='tensorboard.data.ReadScalarsResponse.TagEntry.data', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1789,
  serialized_end=1861,
)

_READSCALARSRESPONSE = _descriptor.Descriptor(
  name='ReadScalarsResponse',
  full_name='tensorboard.data.ReadScalarsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='runs', full_name='tensorboard.data.ReadScalarsResponse.runs', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_READSCALARSRESPONSE_RUNENTRY, _READSCALARSRESPONSE_TAGENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1612,
  serialized_end=1861,
)


_SCALARDATA = _descriptor.Descriptor(
  name='ScalarData',
  full_name='tensorboard.data.ScalarData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='step', full_name='tensorboard.data.ScalarData.step', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='wall_time', full_name='tensorboard.data.ScalarData.wall_time', index=1,
      number=2, type=1, cpp_type=5, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorboard.data.ScalarData.value', index=2,
      number=3, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1863,
  serialized_end=1923,
)


_LISTTENSORSREQUEST = _descriptor.Descriptor(
  name='ListTensorsRequest',
  full_name='tensorboard.data.ListTensorsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='experiment_id', full_name='tensorboard.data.ListTensorsRequest.experiment_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plugin_filter', full_name='tensorboard.data.ListTensorsRequest.plugin_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='run_tag_filter', full_name='tensorboard.data.ListTensorsRequest.run_tag_filter', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1926,
  serialized_end=2080,
)


_LISTTENSORSRESPONSE_RUNENTRY = _descriptor.Descriptor(
  name='RunEntry',
  full_name='tensorboard.data.ListTensorsResponse.RunEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='run_name', full_name='tensorboard.data.ListTensorsResponse.RunEntry.run_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tags', full_name='tensorboard.data.ListTensorsResponse.RunEntry.tags', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2168,
  serialized_end=2258,
)

_LISTTENSORSRESPONSE_TAGENTRY = _descriptor.Descriptor(
  name='TagEntry',
  full_name='tensorboard.data.ListTensorsResponse.TagEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='tensorboard.data.ListTensorsResponse.TagEntry.tag_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='metadata', full_name='tensorboard.data.ListTensorsResponse.TagEntry.metadata', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2260,
  serialized_end=2340,
)

_LISTTENSORSRESPONSE = _descriptor.Descriptor(
  name='ListTensorsResponse',
  full_name='tensorboard.data.ListTensorsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='runs', full_name='tensorboard.data.ListTensorsResponse.runs', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_LISTTENSORSRESPONSE_RUNENTRY, _LISTTENSORSRESPONSE_TAGENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2083,
  serialized_end=2340,
)


_TENSORMETADATA = _descriptor.Descriptor(
  name='TensorMetadata',
  full_name='tensorboard.data.TensorMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='max_step', full_name='tensorboard.data.TensorMetadata.max_step', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_wall_time', full_name='tensorboard.data.TensorMetadata.max_wall_time', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='summary_metadata', full_name='tensorboard.data.TensorMetadata.summary_metadata', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2342,
  serialized_end=2455,
)


_READTENSORSREQUEST = _descriptor.Descriptor(
  name='ReadTensorsRequest',
  full_name='tensorboard.data.ReadTensorsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='experiment_id', full_name='tensorboard.data.ReadTensorsRequest.experiment_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plugin_filter', full_name='tensorboard.data.ReadTensorsRequest.plugin_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='run_tag_filter', full_name='tensorboard.data.ReadTensorsRequest.run_tag_filter', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='downsample', full_name='tensorboard.data.ReadTensorsRequest.downsample', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2458,
  serialized_end=2662,
)


_READTENSORSRESPONSE_RUNENTRY = _descriptor.Descriptor(
  name='RunEntry',
  full_name='tensorboard.data.ReadTensorsResponse.RunEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='run_name', full_name='tensorboard.data.ReadTensorsResponse.RunEntry.run_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tags', full_name='tensorboard.data.ReadTensorsResponse.RunEntry.tags', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2750,
  serialized_end=2840,
)

_READTENSORSRESPONSE_TAGENTRY = _descriptor.Descriptor(
  name='TagEntry',
  full_name='tensorboard.data.ReadTensorsResponse.TagEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='tensorboard.data.ReadTensorsResponse.TagEntry.tag_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='tensorboard.data.ReadTensorsResponse.TagEntry.data', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2842,
  serialized_end=2914,
)

_READTENSORSRESPONSE = _descriptor.Descriptor(
  name='ReadTensorsResponse',
  full_name='tensorboard.data.ReadTensorsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='runs', full_name='tensorboard.data.ReadTensorsResponse.runs', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_READTENSORSRESPONSE_RUNENTRY, _READTENSORSRESPONSE_TAGENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2665,
  serialized_end=2914,
)


_TENSORDATA = _descriptor.Descriptor(
  name='TensorData',
  full_name='tensorboard.data.TensorData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='step', full_name='tensorboard.data.TensorData.step', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='wall_time', full_name='tensorboard.data.TensorData.wall_time', index=1,
      number=2, type=1, cpp_type=5, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorboard.data.TensorData.value', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2916,
  serialized_end=3002,
)


_LISTBLOBSEQUENCESREQUEST = _descriptor.Descriptor(
  name='ListBlobSequencesRequest',
  full_name='tensorboard.data.ListBlobSequencesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='experiment_id', full_name='tensorboard.data.ListBlobSequencesRequest.experiment_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plugin_filter', full_name='tensorboard.data.ListBlobSequencesRequest.plugin_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='run_tag_filter', full_name='tensorboard.data.ListBlobSequencesRequest.run_tag_filter', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3005,
  serialized_end=3165,
)


_LISTBLOBSEQUENCESRESPONSE_RUNENTRY = _descriptor.Descriptor(
  name='RunEntry',
  full_name='tensorboard.data.ListBlobSequencesResponse.RunEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='run_name', full_name='tensorboard.data.ListBlobSequencesResponse.RunEntry.run_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tags', full_name='tensorboard.data.ListBlobSequencesResponse.RunEntry.tags', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3265,
  serialized_end=3361,
)

_LISTBLOBSEQUENCESRESPONSE_TAGENTRY = _descriptor.Descriptor(
  name='TagEntry',
  full_name='tensorboard.data.ListBlobSequencesResponse.TagEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='tensorboard.data.ListBlobSequencesResponse.TagEntry.tag_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='metadata', full_name='tensorboard.data.ListBlobSequencesResponse.TagEntry.metadata', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3363,
  serialized_end=3449,
)

_LISTBLOBSEQUENCESRESPONSE = _descriptor.Descriptor(
  name='ListBlobSequencesResponse',
  full_name='tensorboard.data.ListBlobSequencesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='runs', full_name='tensorboard.data.ListBlobSequencesResponse.runs', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_LISTBLOBSEQUENCESRESPONSE_RUNENTRY, _LISTBLOBSEQUENCESRESPONSE_TAGENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3168,
  serialized_end=3449,
)


_BLOBSEQUENCEMETADATA = _descriptor.Descriptor(
  name='BlobSequenceMetadata',
  full_name='tensorboard.data.BlobSequenceMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='max_step', full_name='tensorboard.data.BlobSequenceMetadata.max_step', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_wall_time', full_name='tensorboard.data.BlobSequenceMetadata.max_wall_time', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_length', full_name='tensorboard.data.BlobSequenceMetadata.max_length', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='summary_metadata', full_name='tensorboard.data.BlobSequenceMetadata.summary_metadata', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3452,
  serialized_end=3591,
)


_READBLOBSEQUENCESREQUEST = _descriptor.Descriptor(
  name='ReadBlobSequencesRequest',
  full_name='tensorboard.data.ReadBlobSequencesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='experiment_id', full_name='tensorboard.data.ReadBlobSequencesRequest.experiment_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plugin_filter', full_name='tensorboard.data.ReadBlobSequencesRequest.plugin_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='run_tag_filter', full_name='tensorboard.data.ReadBlobSequencesRequest.run_tag_filter', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='downsample', full_name='tensorboard.data.ReadBlobSequencesRequest.downsample', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3594,
  serialized_end=3804,
)


_READBLOBSEQUENCESRESPONSE_RUNENTRY = _descriptor.Descriptor(
  name='RunEntry',
  full_name='tensorboard.data.ReadBlobSequencesResponse.RunEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='run_name', full_name='tensorboard.data.ReadBlobSequencesResponse.RunEntry.run_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tags', full_name='tensorboard.data.ReadBlobSequencesResponse.RunEntry.tags', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3904,
  serialized_end=4000,
)

_READBLOBSEQUENCESRESPONSE_TAGENTRY = _descriptor.Descriptor(
  name='TagEntry',
  full_name='tensorboard.data.ReadBlobSequencesResponse.TagEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='tensorboard.data.ReadBlobSequencesResponse.TagEntry.tag_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='tensorboard.data.ReadBlobSequencesResponse.TagEntry.data', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4002,
  serialized_end=4080,
)

_READBLOBSEQUENCESRESPONSE = _descriptor.Descriptor(
  name='ReadBlobSequencesResponse',
  full_name='tensorboard.data.ReadBlobSequencesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='runs', full_name='tensorboard.data.ReadBlobSequencesResponse.runs', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_READBLOBSEQUENCESRESPONSE_RUNENTRY, _READBLOBSEQUENCESRESPONSE_TAGENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3807,
  serialized_end=4080,
)


_BLOBSEQUENCEDATA = _descriptor.Descriptor(
  name='BlobSequenceData',
  full_name='tensorboard.data.BlobSequenceData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='step', full_name='tensorboard.data.BlobSequenceData.step', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='wall_time', full_name='tensorboard.data.BlobSequenceData.wall_time', index=1,
      number=2, type=1, cpp_type=5, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='values', full_name='tensorboard.data.BlobSequenceData.values', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4082,
  serialized_end=4190,
)


_BLOBREFERENCESEQUENCE = _descriptor.Descriptor(
  name='BlobReferenceSequence',
  full_name='tensorboard.data.BlobReferenceSequence',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='blob_refs', full_name='tensorboard.data.BlobReferenceSequence.blob_refs', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4192,
  serialized_end=4267,
)


_BLOBREFERENCE = _descriptor.Descriptor(
  name='BlobReference',
  full_name='tensorboard.data.BlobReference',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='blob_key', full_name='tensorboard.data.BlobReference.blob_key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='tensorboard.data.BlobReference.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4269,
  serialized_end=4315,
)


_READBLOBREQUEST = _descriptor.Descriptor(
  name='ReadBlobRequest',
  full_name='tensorboard.data.ReadBlobRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='blob_key', full_name='tensorboard.data.ReadBlobRequest.blob_key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4317,
  serialized_end=4352,
)


_READBLOBRESPONSE = _descriptor.Descriptor(
  name='ReadBlobResponse',
  full_name='tensorboard.data.ReadBlobResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='data', full_name='tensorboard.data.ReadBlobResponse.data', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4354,
  serialized_end=4386,
)

_GETEXPERIMENTRESPONSE.fields_by_name['creation_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RUNTAGFILTER.fields_by_name['runs'].message_type = _RUNFILTER
_RUNTAGFILTER.fields_by_name['tags'].message_type = _TAGFILTER
_LISTPLUGINSRESPONSE.fields_by_name['plugins'].message_type = _PLUGIN
_LISTRUNSRESPONSE.fields_by_name['runs'].message_type = _RUN
_LISTSCALARSREQUEST.fields_by_name['plugin_filter'].message_type = _PLUGINFILTER
_LISTSCALARSREQUEST.fields_by_name['run_tag_filter'].message_type = _RUNTAGFILTER
_LISTSCALARSRESPONSE_RUNENTRY.fields_by_name['tags'].message_type = _LISTSCALARSRESPONSE_TAGENTRY
_LISTSCALARSRESPONSE_RUNENTRY.containing_type = _LISTSCALARSRESPONSE
_LISTSCALARSRESPONSE_TAGENTRY.fields_by_name['metadata'].message_type = _SCALARMETADATA
_LISTSCALARSRESPONSE_TAGENTRY.containing_type = _LISTSCALARSRESPONSE
_LISTSCALARSRESPONSE.fields_by_name['runs'].message_type = _LISTSCALARSRESPONSE_RUNENTRY
_SCALARMETADATA.fields_by_name['summary_metadata'].message_type = tensorboard_dot_compat_dot_proto_dot_summary__pb2._SUMMARYMETADATA
_READSCALARSREQUEST.fields_by_name['plugin_filter'].message_type = _PLUGINFILTER
_READSCALARSREQUEST.fields_by_name['run_tag_filter'].message_type = _RUNTAGFILTER
_READSCALARSREQUEST.fields_by_name['downsample'].message_type = _DOWNSAMPLE
_READSCALARSRESPONSE_RUNENTRY.fields_by_name['tags'].message_type = _READSCALARSRESPONSE_TAGENTRY
_READSCALARSRESPONSE_RUNENTRY.containing_type = _READSCALARSRESPONSE
_READSCALARSRESPONSE_TAGENTRY.fields_by_name['data'].message_type = _SCALARDATA
_READSCALARSRESPONSE_TAGENTRY.containing_type = _READSCALARSRESPONSE
_READSCALARSRESPONSE.fields_by_name['runs'].message_type = _READSCALARSRESPONSE_RUNENTRY
_LISTTENSORSREQUEST.fields_by_name['plugin_filter'].message_type = _PLUGINFILTER
_LISTTENSORSREQUEST.fields_by_name['run_tag_filter'].message_type = _RUNTAGFILTER
_LISTTENSORSRESPONSE_RUNENTRY.fields_by_name['tags'].message_type = _LISTTENSORSRESPONSE_TAGENTRY
_LISTTENSORSRESPONSE_RUNENTRY.containing_type = _LISTTENSORSRESPONSE
_LISTTENSORSRESPONSE_TAGENTRY.fields_by_name['metadata'].message_type = _TENSORMETADATA
_LISTTENSORSRESPONSE_TAGENTRY.containing_type = _LISTTENSORSRESPONSE
_LISTTENSORSRESPONSE.fields_by_name['runs'].message_type = _LISTTENSORSRESPONSE_RUNENTRY
_TENSORMETADATA.fields_by_name['summary_metadata'].message_type = tensorboard_dot_compat_dot_proto_dot_summary__pb2._SUMMARYMETADATA
_READTENSORSREQUEST.fields_by_name['plugin_filter'].message_type = _PLUGINFILTER
_READTENSORSREQUEST.fields_by_name['run_tag_filter'].message_type = _RUNTAGFILTER
_READTENSORSREQUEST.fields_by_name['downsample'].message_type = _DOWNSAMPLE
_READTENSORSRESPONSE_RUNENTRY.fields_by_name['tags'].message_type = _READTENSORSRESPONSE_TAGENTRY
_READTENSORSRESPONSE_RUNENTRY.containing_type = _READTENSORSRESPONSE
_READTENSORSRESPONSE_TAGENTRY.fields_by_name['data'].message_type = _TENSORDATA
_READTENSORSRESPONSE_TAGENTRY.containing_type = _READTENSORSRESPONSE
_READTENSORSRESPONSE.fields_by_name['runs'].message_type = _READTENSORSRESPONSE_RUNENTRY
_TENSORDATA.fields_by_name['value'].message_type = tensorboard_dot_compat_dot_proto_dot_tensor__pb2._TENSORPROTO
_LISTBLOBSEQUENCESREQUEST.fields_by_name['plugin_filter'].message_type = _PLUGINFILTER
_LISTBLOBSEQUENCESREQUEST.fields_by_name['run_tag_filter'].message_type = _RUNTAGFILTER
_LISTBLOBSEQUENCESRESPONSE_RUNENTRY.fields_by_name['tags'].message_type = _LISTBLOBSEQUENCESRESPONSE_TAGENTRY
_LISTBLOBSEQUENCESRESPONSE_RUNENTRY.containing_type = _LISTBLOBSEQUENCESRESPONSE
_LISTBLOBSEQUENCESRESPONSE_TAGENTRY.fields_by_name['metadata'].message_type = _BLOBSEQUENCEMETADATA
_LISTBLOBSEQUENCESRESPONSE_TAGENTRY.containing_type = _LISTBLOBSEQUENCESRESPONSE
_LISTBLOBSEQUENCESRESPONSE.fields_by_name['runs'].message_type = _LISTBLOBSEQUENCESRESPONSE_RUNENTRY
_BLOBSEQUENCEMETADATA.fields_by_name['summary_metadata'].message_type = tensorboard_dot_compat_dot_proto_dot_summary__pb2._SUMMARYMETADATA
_READBLOBSEQUENCESREQUEST.fields_by_name['plugin_filter'].message_type = _PLUGINFILTER
_READBLOBSEQUENCESREQUEST.fields_by_name['run_tag_filter'].message_type = _RUNTAGFILTER
_READBLOBSEQUENCESREQUEST.fields_by_name['downsample'].message_type = _DOWNSAMPLE
_READBLOBSEQUENCESRESPONSE_RUNENTRY.fields_by_name['tags'].message_type = _READBLOBSEQUENCESRESPONSE_TAGENTRY
_READBLOBSEQUENCESRESPONSE_RUNENTRY.containing_type = _READBLOBSEQUENCESRESPONSE
_READBLOBSEQUENCESRESPONSE_TAGENTRY.fields_by_name['data'].message_type = _BLOBSEQUENCEDATA
_READBLOBSEQUENCESRESPONSE_TAGENTRY.containing_type = _READBLOBSEQUENCESRESPONSE
_READBLOBSEQUENCESRESPONSE.fields_by_name['runs'].message_type = _READBLOBSEQUENCESRESPONSE_RUNENTRY
_BLOBSEQUENCEDATA.fields_by_name['values'].message_type = _BLOBREFERENCESEQUENCE
_BLOBREFERENCESEQUENCE.fields_by_name['blob_refs'].message_type = _BLOBREFERENCE
DESCRIPTOR.message_types_by_name['GetExperimentRequest'] = _GETEXPERIMENTREQUEST
DESCRIPTOR.message_types_by_name['GetExperimentResponse'] = _GETEXPERIMENTRESPONSE
DESCRIPTOR.message_types_by_name['PluginFilter'] = _PLUGINFILTER
DESCRIPTOR.message_types_by_name['RunTagFilter'] = _RUNTAGFILTER
DESCRIPTOR.message_types_by_name['RunFilter'] = _RUNFILTER
DESCRIPTOR.message_types_by_name['TagFilter'] = _TAGFILTER
DESCRIPTOR.message_types_by_name['Downsample'] = _DOWNSAMPLE
DESCRIPTOR.message_types_by_name['ListPluginsRequest'] = _LISTPLUGINSREQUEST
DESCRIPTOR.message_types_by_name['ListPluginsResponse'] = _LISTPLUGINSRESPONSE
DESCRIPTOR.message_types_by_name['Plugin'] = _PLUGIN
DESCRIPTOR.message_types_by_name['ListRunsRequest'] = _LISTRUNSREQUEST
DESCRIPTOR.message_types_by_name['ListRunsResponse'] = _LISTRUNSRESPONSE
DESCRIPTOR.message_types_by_name['Run'] = _RUN
DESCRIPTOR.message_types_by_name['ListScalarsRequest'] = _LISTSCALARSREQUEST
DESCRIPTOR.message_types_by_name['ListScalarsResponse'] = _LISTSCALARSRESPONSE
DESCRIPTOR.message_types_by_name['ScalarMetadata'] = _SCALARMETADATA
DESCRIPTOR.message_types_by_name['ReadScalarsRequest'] = _READSCALARSREQUEST
DESCRIPTOR.message_types_by_name['ReadScalarsResponse'] = _READSCALARSRESPONSE
DESCRIPTOR.message_types_by_name['ScalarData'] = _SCALARDATA
DESCRIPTOR.message_types_by_name['ListTensorsRequest'] = _LISTTENSORSREQUEST
DESCRIPTOR.message_types_by_name['ListTensorsResponse'] = _LISTTENSORSRESPONSE
DESCRIPTOR.message_types_by_name['TensorMetadata'] = _TENSORMETADATA
DESCRIPTOR.message_types_by_name['ReadTensorsRequest'] = _READTENSORSREQUEST
DESCRIPTOR.message_types_by_name['ReadTensorsResponse'] = _READTENSORSRESPONSE
DESCRIPTOR.message_types_by_name['TensorData'] = _TENSORDATA
DESCRIPTOR.message_types_by_name['ListBlobSequencesRequest'] = _LISTBLOBSEQUENCESREQUEST
DESCRIPTOR.message_types_by_name['ListBlobSequencesResponse'] = _LISTBLOBSEQUENCESRESPONSE
DESCRIPTOR.message_types_by_name['BlobSequenceMetadata'] = _BLOBSEQUENCEMETADATA
DESCRIPTOR.message_types_by_name['ReadBlobSequencesRequest'] = _READBLOBSEQUENCESREQUEST
DESCRIPTOR.message_types_by_name['ReadBlobSequencesResponse'] = _READBLOBSEQUENCESRESPONSE
DESCRIPTOR.message_types_by_name['BlobSequenceData'] = _BLOBSEQUENCEDATA
DESCRIPTOR.message_types_by_name['BlobReferenceSequence'] = _BLOBREFERENCESEQUENCE
DESCRIPTOR.message_types_by_name['BlobReference'] = _BLOBREFERENCE
DESCRIPTOR.message_types_by_name['ReadBlobRequest'] = _READBLOBREQUEST
DESCRIPTOR.message_types_by_name['ReadBlobResponse'] = _READBLOBRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetExperimentRequest = _reflection.GeneratedProtocolMessageType('GetExperimentRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETEXPERIMENTREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.GetExperimentRequest)
  })
_sym_db.RegisterMessage(GetExperimentRequest)

GetExperimentResponse = _reflection.GeneratedProtocolMessageType('GetExperimentResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETEXPERIMENTRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.GetExperimentResponse)
  })
_sym_db.RegisterMessage(GetExperimentResponse)

PluginFilter = _reflection.GeneratedProtocolMessageType('PluginFilter', (_message.Message,), {
  'DESCRIPTOR' : _PLUGINFILTER,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.PluginFilter)
  })
_sym_db.RegisterMessage(PluginFilter)

RunTagFilter = _reflection.GeneratedProtocolMessageType('RunTagFilter', (_message.Message,), {
  'DESCRIPTOR' : _RUNTAGFILTER,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.RunTagFilter)
  })
_sym_db.RegisterMessage(RunTagFilter)

RunFilter = _reflection.GeneratedProtocolMessageType('RunFilter', (_message.Message,), {
  'DESCRIPTOR' : _RUNFILTER,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.RunFilter)
  })
_sym_db.RegisterMessage(RunFilter)

TagFilter = _reflection.GeneratedProtocolMessageType('TagFilter', (_message.Message,), {
  'DESCRIPTOR' : _TAGFILTER,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.TagFilter)
  })
_sym_db.RegisterMessage(TagFilter)

Downsample = _reflection.GeneratedProtocolMessageType('Downsample', (_message.Message,), {
  'DESCRIPTOR' : _DOWNSAMPLE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.Downsample)
  })
_sym_db.RegisterMessage(Downsample)

ListPluginsRequest = _reflection.GeneratedProtocolMessageType('ListPluginsRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTPLUGINSREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListPluginsRequest)
  })
_sym_db.RegisterMessage(ListPluginsRequest)

ListPluginsResponse = _reflection.GeneratedProtocolMessageType('ListPluginsResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTPLUGINSRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListPluginsResponse)
  })
_sym_db.RegisterMessage(ListPluginsResponse)

Plugin = _reflection.GeneratedProtocolMessageType('Plugin', (_message.Message,), {
  'DESCRIPTOR' : _PLUGIN,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.Plugin)
  })
_sym_db.RegisterMessage(Plugin)

ListRunsRequest = _reflection.GeneratedProtocolMessageType('ListRunsRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTRUNSREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListRunsRequest)
  })
_sym_db.RegisterMessage(ListRunsRequest)

ListRunsResponse = _reflection.GeneratedProtocolMessageType('ListRunsResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTRUNSRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListRunsResponse)
  })
_sym_db.RegisterMessage(ListRunsResponse)

Run = _reflection.GeneratedProtocolMessageType('Run', (_message.Message,), {
  'DESCRIPTOR' : _RUN,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.Run)
  })
_sym_db.RegisterMessage(Run)

ListScalarsRequest = _reflection.GeneratedProtocolMessageType('ListScalarsRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTSCALARSREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListScalarsRequest)
  })
_sym_db.RegisterMessage(ListScalarsRequest)

ListScalarsResponse = _reflection.GeneratedProtocolMessageType('ListScalarsResponse', (_message.Message,), {

  'RunEntry' : _reflection.GeneratedProtocolMessageType('RunEntry', (_message.Message,), {
    'DESCRIPTOR' : _LISTSCALARSRESPONSE_RUNENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ListScalarsResponse.RunEntry)
    })
  ,

  'TagEntry' : _reflection.GeneratedProtocolMessageType('TagEntry', (_message.Message,), {
    'DESCRIPTOR' : _LISTSCALARSRESPONSE_TAGENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ListScalarsResponse.TagEntry)
    })
  ,
  'DESCRIPTOR' : _LISTSCALARSRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListScalarsResponse)
  })
_sym_db.RegisterMessage(ListScalarsResponse)
_sym_db.RegisterMessage(ListScalarsResponse.RunEntry)
_sym_db.RegisterMessage(ListScalarsResponse.TagEntry)

ScalarMetadata = _reflection.GeneratedProtocolMessageType('ScalarMetadata', (_message.Message,), {
  'DESCRIPTOR' : _SCALARMETADATA,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ScalarMetadata)
  })
_sym_db.RegisterMessage(ScalarMetadata)

ReadScalarsRequest = _reflection.GeneratedProtocolMessageType('ReadScalarsRequest', (_message.Message,), {
  'DESCRIPTOR' : _READSCALARSREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ReadScalarsRequest)
  })
_sym_db.RegisterMessage(ReadScalarsRequest)

ReadScalarsResponse = _reflection.GeneratedProtocolMessageType('ReadScalarsResponse', (_message.Message,), {

  'RunEntry' : _reflection.GeneratedProtocolMessageType('RunEntry', (_message.Message,), {
    'DESCRIPTOR' : _READSCALARSRESPONSE_RUNENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ReadScalarsResponse.RunEntry)
    })
  ,

  'TagEntry' : _reflection.GeneratedProtocolMessageType('TagEntry', (_message.Message,), {
    'DESCRIPTOR' : _READSCALARSRESPONSE_TAGENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ReadScalarsResponse.TagEntry)
    })
  ,
  'DESCRIPTOR' : _READSCALARSRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ReadScalarsResponse)
  })
_sym_db.RegisterMessage(ReadScalarsResponse)
_sym_db.RegisterMessage(ReadScalarsResponse.RunEntry)
_sym_db.RegisterMessage(ReadScalarsResponse.TagEntry)

ScalarData = _reflection.GeneratedProtocolMessageType('ScalarData', (_message.Message,), {
  'DESCRIPTOR' : _SCALARDATA,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ScalarData)
  })
_sym_db.RegisterMessage(ScalarData)

ListTensorsRequest = _reflection.GeneratedProtocolMessageType('ListTensorsRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTTENSORSREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListTensorsRequest)
  })
_sym_db.RegisterMessage(ListTensorsRequest)

ListTensorsResponse = _reflection.GeneratedProtocolMessageType('ListTensorsResponse', (_message.Message,), {

  'RunEntry' : _reflection.GeneratedProtocolMessageType('RunEntry', (_message.Message,), {
    'DESCRIPTOR' : _LISTTENSORSRESPONSE_RUNENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ListTensorsResponse.RunEntry)
    })
  ,

  'TagEntry' : _reflection.GeneratedProtocolMessageType('TagEntry', (_message.Message,), {
    'DESCRIPTOR' : _LISTTENSORSRESPONSE_TAGENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ListTensorsResponse.TagEntry)
    })
  ,
  'DESCRIPTOR' : _LISTTENSORSRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListTensorsResponse)
  })
_sym_db.RegisterMessage(ListTensorsResponse)
_sym_db.RegisterMessage(ListTensorsResponse.RunEntry)
_sym_db.RegisterMessage(ListTensorsResponse.TagEntry)

TensorMetadata = _reflection.GeneratedProtocolMessageType('TensorMetadata', (_message.Message,), {
  'DESCRIPTOR' : _TENSORMETADATA,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.TensorMetadata)
  })
_sym_db.RegisterMessage(TensorMetadata)

ReadTensorsRequest = _reflection.GeneratedProtocolMessageType('ReadTensorsRequest', (_message.Message,), {
  'DESCRIPTOR' : _READTENSORSREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ReadTensorsRequest)
  })
_sym_db.RegisterMessage(ReadTensorsRequest)

ReadTensorsResponse = _reflection.GeneratedProtocolMessageType('ReadTensorsResponse', (_message.Message,), {

  'RunEntry' : _reflection.GeneratedProtocolMessageType('RunEntry', (_message.Message,), {
    'DESCRIPTOR' : _READTENSORSRESPONSE_RUNENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ReadTensorsResponse.RunEntry)
    })
  ,

  'TagEntry' : _reflection.GeneratedProtocolMessageType('TagEntry', (_message.Message,), {
    'DESCRIPTOR' : _READTENSORSRESPONSE_TAGENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ReadTensorsResponse.TagEntry)
    })
  ,
  'DESCRIPTOR' : _READTENSORSRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ReadTensorsResponse)
  })
_sym_db.RegisterMessage(ReadTensorsResponse)
_sym_db.RegisterMessage(ReadTensorsResponse.RunEntry)
_sym_db.RegisterMessage(ReadTensorsResponse.TagEntry)

TensorData = _reflection.GeneratedProtocolMessageType('TensorData', (_message.Message,), {
  'DESCRIPTOR' : _TENSORDATA,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.TensorData)
  })
_sym_db.RegisterMessage(TensorData)

ListBlobSequencesRequest = _reflection.GeneratedProtocolMessageType('ListBlobSequencesRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTBLOBSEQUENCESREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListBlobSequencesRequest)
  })
_sym_db.RegisterMessage(ListBlobSequencesRequest)

ListBlobSequencesResponse = _reflection.GeneratedProtocolMessageType('ListBlobSequencesResponse', (_message.Message,), {

  'RunEntry' : _reflection.GeneratedProtocolMessageType('RunEntry', (_message.Message,), {
    'DESCRIPTOR' : _LISTBLOBSEQUENCESRESPONSE_RUNENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ListBlobSequencesResponse.RunEntry)
    })
  ,

  'TagEntry' : _reflection.GeneratedProtocolMessageType('TagEntry', (_message.Message,), {
    'DESCRIPTOR' : _LISTBLOBSEQUENCESRESPONSE_TAGENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ListBlobSequencesResponse.TagEntry)
    })
  ,
  'DESCRIPTOR' : _LISTBLOBSEQUENCESRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListBlobSequencesResponse)
  })
_sym_db.RegisterMessage(ListBlobSequencesResponse)
_sym_db.RegisterMessage(ListBlobSequencesResponse.RunEntry)
_sym_db.RegisterMessage(ListBlobSequencesResponse.TagEntry)

BlobSequenceMetadata = _reflection.GeneratedProtocolMessageType('BlobSequenceMetadata', (_message.Message,), {
  'DESCRIPTOR' : _BLOBSEQUENCEMETADATA,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.BlobSequenceMetadata)
  })
_sym_db.RegisterMessage(BlobSequenceMetadata)

ReadBlobSequencesRequest = _reflection.GeneratedProtocolMessageType('ReadBlobSequencesRequest', (_message.Message,), {
  'DESCRIPTOR' : _READBLOBSEQUENCESREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ReadBlobSequencesRequest)
  })
_sym_db.RegisterMessage(ReadBlobSequencesRequest)

ReadBlobSequencesResponse = _reflection.GeneratedProtocolMessageType('ReadBlobSequencesResponse', (_message.Message,), {

  'RunEntry' : _reflection.GeneratedProtocolMessageType('RunEntry', (_message.Message,), {
    'DESCRIPTOR' : _READBLOBSEQUENCESRESPONSE_RUNENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ReadBlobSequencesResponse.RunEntry)
    })
  ,

  'TagEntry' : _reflection.GeneratedProtocolMessageType('TagEntry', (_message.Message,), {
    'DESCRIPTOR' : _READBLOBSEQUENCESRESPONSE_TAGENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ReadBlobSequencesResponse.TagEntry)
    })
  ,
  'DESCRIPTOR' : _READBLOBSEQUENCESRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ReadBlobSequencesResponse)
  })
_sym_db.RegisterMessage(ReadBlobSequencesResponse)
_sym_db.RegisterMessage(ReadBlobSequencesResponse.RunEntry)
_sym_db.RegisterMessage(ReadBlobSequencesResponse.TagEntry)

BlobSequenceData = _reflection.GeneratedProtocolMessageType('BlobSequenceData', (_message.Message,), {
  'DESCRIPTOR' : _BLOBSEQUENCEDATA,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.BlobSequenceData)
  })
_sym_db.RegisterMessage(BlobSequenceData)

BlobReferenceSequence = _reflection.GeneratedProtocolMessageType('BlobReferenceSequence', (_message.Message,), {
  'DESCRIPTOR' : _BLOBREFERENCESEQUENCE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.BlobReferenceSequence)
  })
_sym_db.RegisterMessage(BlobReferenceSequence)

BlobReference = _reflection.GeneratedProtocolMessageType('BlobReference', (_message.Message,), {
  'DESCRIPTOR' : _BLOBREFERENCE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.BlobReference)
  })
_sym_db.RegisterMessage(BlobReference)

ReadBlobRequest = _reflection.GeneratedProtocolMessageType('ReadBlobRequest', (_message.Message,), {
  'DESCRIPTOR' : _READBLOBREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ReadBlobRequest)
  })
_sym_db.RegisterMessage(ReadBlobRequest)

ReadBlobResponse = _reflection.GeneratedProtocolMessageType('ReadBlobResponse', (_message.Message,), {
  'DESCRIPTOR' : _READBLOBRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ReadBlobResponse)
  })
_sym_db.RegisterMessage(ReadBlobResponse)


DESCRIPTOR._options = None

_TENSORBOARDDATAPROVIDER = _descriptor.ServiceDescriptor(
  name='TensorBoardDataProvider',
  full_name='tensorboard.data.TensorBoardDataProvider',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=4389,
  serialized_end=5378,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetExperiment',
    full_name='tensorboard.data.TensorBoardDataProvider.GetExperiment',
    index=0,
    containing_service=None,
    input_type=_GETEXPERIMENTREQUEST,
    output_type=_GETEXPERIMENTRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='ListPlugins',
    full_name='tensorboard.data.TensorBoardDataProvider.ListPlugins',
    index=1,
    containing_service=None,
    input_type=_LISTPLUGINSREQUEST,
    output_type=_LISTPLUGINSRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='ListRuns',
    full_name='tensorboard.data.TensorBoardDataProvider.ListRuns',
    index=2,
    containing_service=None,
    input_type=_LISTRUNSREQUEST,
    output_type=_LISTRUNSRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='ListScalars',
    full_name='tensorboard.data.TensorBoardDataProvider.ListScalars',
    index=3,
    containing_service=None,
    input_type=_LISTSCALARSREQUEST,
    output_type=_LISTSCALARSRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='ReadScalars',
    full_name='tensorboard.data.TensorBoardDataProvider.ReadScalars',
    index=4,
    containing_service=None,
    input_type=_READSCALARSREQUEST,
    output_type=_READSCALARSRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='ListTensors',
    full_name='tensorboard.data.TensorBoardDataProvider.ListTensors',
    index=5,
    containing_service=None,
    input_type=_LISTTENSORSREQUEST,
    output_type=_LISTTENSORSRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='ReadTensors',
    full_name='tensorboard.data.TensorBoardDataProvider.ReadTensors',
    index=6,
    containing_service=None,
    input_type=_READTENSORSREQUEST,
    output_type=_READTENSORSRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='ListBlobSequences',
    full_name='tensorboard.data.TensorBoardDataProvider.ListBlobSequences',
    index=7,
    containing_service=None,
    input_type=_LISTBLOBSEQUENCESREQUEST,
    output_type=_LISTBLOBSEQUENCESRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='ReadBlobSequences',
    full_name='tensorboard.data.TensorBoardDataProvider.ReadBlobSequences',
    index=8,
    containing_service=None,
    input_type=_READBLOBSEQUENCESREQUEST,
    output_type=_READBLOBSEQUENCESRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='ReadBlob',
    full_name='tensorboard.data.TensorBoardDataProvider.ReadBlob',
    index=9,
    containing_service=None,
    input_type=_READBLOBREQUEST,
    output_type=_READBLOBRESPONSE,
    serialized_options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_TENSORBOARDDATAPROVIDER)

DESCRIPTOR.services_by_name['TensorBoardDataProvider'] = _TENSORBOARDDATAPROVIDER

# @@protoc_insertion_point(module_scope)
