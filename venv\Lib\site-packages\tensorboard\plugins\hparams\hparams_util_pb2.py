# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/plugins/hparams/hparams_util.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from tensorboard.plugins.hparams import api_pb2 as tensorboard_dot_plugins_dot_hparams_dot_api__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorboard/plugins/hparams/hparams_util.proto',
  package='tensorboard.hparams',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n.tensorboard/plugins/hparams/hparams_util.proto\x12\x13tensorboard.hparams\x1a\x1cgoogle/protobuf/struct.proto\x1a%tensorboard/plugins/hparams/api.proto\"H\n\x0fHParamInfosList\x12\x35\n\x0chparam_infos\x18\x01 \x03(\x0b\x32\x1f.tensorboard.hparams.HParamInfo\"H\n\x0fMetricInfosList\x12\x35\n\x0cmetric_infos\x18\x01 \x03(\x0b\x32\x1f.tensorboard.hparams.MetricInfo\"\x8d\x01\n\x07HParams\x12:\n\x07hparams\x18\x01 \x03(\x0b\x32).tensorboard.hparams.HParams.HparamsEntry\x1a\x46\n\x0cHparamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01\x62\x06proto3')
  ,
  dependencies=[google_dot_protobuf_dot_struct__pb2.DESCRIPTOR,tensorboard_dot_plugins_dot_hparams_dot_api__pb2.DESCRIPTOR,])




_HPARAMINFOSLIST = _descriptor.Descriptor(
  name='HParamInfosList',
  full_name='tensorboard.hparams.HParamInfosList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='hparam_infos', full_name='tensorboard.hparams.HParamInfosList.hparam_infos', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=140,
  serialized_end=212,
)


_METRICINFOSLIST = _descriptor.Descriptor(
  name='MetricInfosList',
  full_name='tensorboard.hparams.MetricInfosList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='metric_infos', full_name='tensorboard.hparams.MetricInfosList.metric_infos', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=214,
  serialized_end=286,
)


_HPARAMS_HPARAMSENTRY = _descriptor.Descriptor(
  name='HparamsEntry',
  full_name='tensorboard.hparams.HParams.HparamsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorboard.hparams.HParams.HparamsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorboard.hparams.HParams.HparamsEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=360,
  serialized_end=430,
)

_HPARAMS = _descriptor.Descriptor(
  name='HParams',
  full_name='tensorboard.hparams.HParams',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='hparams', full_name='tensorboard.hparams.HParams.hparams', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_HPARAMS_HPARAMSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=289,
  serialized_end=430,
)

_HPARAMINFOSLIST.fields_by_name['hparam_infos'].message_type = tensorboard_dot_plugins_dot_hparams_dot_api__pb2._HPARAMINFO
_METRICINFOSLIST.fields_by_name['metric_infos'].message_type = tensorboard_dot_plugins_dot_hparams_dot_api__pb2._METRICINFO
_HPARAMS_HPARAMSENTRY.fields_by_name['value'].message_type = google_dot_protobuf_dot_struct__pb2._VALUE
_HPARAMS_HPARAMSENTRY.containing_type = _HPARAMS
_HPARAMS.fields_by_name['hparams'].message_type = _HPARAMS_HPARAMSENTRY
DESCRIPTOR.message_types_by_name['HParamInfosList'] = _HPARAMINFOSLIST
DESCRIPTOR.message_types_by_name['MetricInfosList'] = _METRICINFOSLIST
DESCRIPTOR.message_types_by_name['HParams'] = _HPARAMS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

HParamInfosList = _reflection.GeneratedProtocolMessageType('HParamInfosList', (_message.Message,), {
  'DESCRIPTOR' : _HPARAMINFOSLIST,
  '__module__' : 'tensorboard.plugins.hparams.hparams_util_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.HParamInfosList)
  })
_sym_db.RegisterMessage(HParamInfosList)

MetricInfosList = _reflection.GeneratedProtocolMessageType('MetricInfosList', (_message.Message,), {
  'DESCRIPTOR' : _METRICINFOSLIST,
  '__module__' : 'tensorboard.plugins.hparams.hparams_util_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.MetricInfosList)
  })
_sym_db.RegisterMessage(MetricInfosList)

HParams = _reflection.GeneratedProtocolMessageType('HParams', (_message.Message,), {

  'HparamsEntry' : _reflection.GeneratedProtocolMessageType('HparamsEntry', (_message.Message,), {
    'DESCRIPTOR' : _HPARAMS_HPARAMSENTRY,
    '__module__' : 'tensorboard.plugins.hparams.hparams_util_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.hparams.HParams.HparamsEntry)
    })
  ,
  'DESCRIPTOR' : _HPARAMS,
  '__module__' : 'tensorboard.plugins.hparams.hparams_util_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.hparams.HParams)
  })
_sym_db.RegisterMessage(HParams)
_sym_db.RegisterMessage(HParams.HparamsEntry)


_HPARAMS_HPARAMSENTRY._options = None
# @@protoc_insertion_point(module_scope)
