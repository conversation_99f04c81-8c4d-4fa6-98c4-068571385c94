
# This file was generated by 'versioneer.py' (0.18) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2020-07-19T18:35:29-0400",
 "dirty": false,
 "error": null,
 "full-revisionid": "c826bb7df16f470a69f7bf90598fc27586209d11",
 "version": "v3.3.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
